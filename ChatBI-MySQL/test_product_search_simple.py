#!/usr/bin/env python3
"""
简化的商品搜索工具迁移测试

验证配置文件更新和基本功能，避免数据库连接。
"""
import yaml
import sys
from pathlib import Path


def test_configuration_files():
    """测试配置文件更新"""
    print("📝 测试配置文件更新")
    
    try:
        # 检查general_chat_bot.yml
        with open("resources/data_fetcher_bot_config/general_chat_bot.yml", 'r', encoding='utf-8') as f:
            general_content = f.read()
        
        general_config = yaml.safe_load(general_content)
        tools_config = general_config.get('tools', [])
        tool_names = [tool.get('name') for tool in tools_config if isinstance(tool, dict)]
        
        if "search_product_by_name" not in tool_names:
            print("  ✅ general_chat_bot.yml 工具配置已移除商品搜索工具")
        else:
            print("  ❌ general_chat_bot.yml 工具配置仍包含商品搜索工具")
            return False
        
        if "主助手进行商品搜索" in general_content:
            print("  ✅ general_chat_bot.yml 已添加引导说明")
        else:
            print("  ⚠️ general_chat_bot.yml 可能缺少引导说明")
        
        # 检查warehouse_and_fulfillment.yml
        with open("resources/data_fetcher_bot_config/warehouse_and_fulfillment.yml", 'r', encoding='utf-8') as f:
            warehouse_content = f.read()
        
        warehouse_config = yaml.safe_load(warehouse_content)
        warehouse_tools = warehouse_config.get('tools', [])
        warehouse_tool_names = [tool.get('name') for tool in warehouse_tools if isinstance(tool, dict)]
        
        if "search_product_by_name" not in warehouse_tool_names:
            print("  ✅ warehouse_and_fulfillment.yml 工具配置已移除商品搜索工具")
        else:
            print("  ❌ warehouse_and_fulfillment.yml 工具配置仍包含商品搜索工具")
            return False
        
        if "主助手进行商品搜索" in warehouse_content:
            print("  ✅ warehouse_and_fulfillment.yml 已添加引导说明")
        else:
            print("  ⚠️ warehouse_and_fulfillment.yml 可能缺少引导说明")
        
        # 检查coordinator_instruction.md
        with open("resources/prompt/coordinator_instruction.md", 'r', encoding='utf-8') as f:
            coordinator_content = f.read()
        
        if "商品搜索 (search_product_by_name)" in coordinator_content:
            print("  ✅ coordinator_instruction.md 已添加商品搜索说明")
        else:
            print("  ❌ coordinator_instruction.md 缺少商品搜索说明")
            return False
        
        if "必须与用户确认具体SKU" in coordinator_content:
            print("  ✅ coordinator_instruction.md 包含用户确认说明")
        else:
            print("  ❌ coordinator_instruction.md 缺少用户确认说明")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试配置文件更新失败: {e}")
        return False


def test_coordinator_bot_code():
    """测试协调者Bot代码更新"""
    print("\n🤖 测试协调者Bot代码更新")
    
    try:
        # 检查coordinator_bot.py
        with open("src/services/agent/bots/coordinator_bot.py", 'r', encoding='utf-8') as f:
            coordinator_code = f.read()
        
        if "_add_product_search_tools" in coordinator_code:
            print("  ✅ 协调者Bot包含商品搜索工具添加方法")
        else:
            print("  ❌ 协调者Bot缺少商品搜索工具添加方法")
            return False
        
        if "search_product_by_name" in coordinator_code:
            print("  ✅ 协调者Bot导入了商品搜索工具")
        else:
            print("  ❌ 协调者Bot未导入商品搜索工具")
            return False
        
        if "已添加商品搜索工具到协调者Bot" in coordinator_code:
            print("  ✅ 协调者Bot包含商品搜索工具日志")
        else:
            print("  ❌ 协调者Bot缺少商品搜索工具日志")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试协调者Bot代码失败: {e}")
        return False


def test_product_search_tool():
    """测试商品搜索工具本身"""
    print("\n🔍 测试商品搜索工具")
    
    try:
        # 检查product_search_tool.py
        with open("src/services/agent/tools/product_search_tool.py", 'r', encoding='utf-8') as f:
            tool_code = f.read()
        
        if "确认" in tool_code:
            print("  ✅ 商品搜索工具包含用户确认相关说明")
        else:
            print("  ⚠️ 商品搜索工具可能缺少用户确认说明")
        
        if "def search_product_by_name" in tool_code:
            print("  ✅ 商品搜索工具函数定义正确")
        else:
            print("  ❌ 商品搜索工具函数定义有问题")
            return False
        
        if "tool_manager.register_as_function_tool" in tool_code:
            print("  ✅ 商品搜索工具已注册到工具管理器")
        else:
            print("  ❌ 商品搜索工具未注册到工具管理器")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试商品搜索工具失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始简化的商品搜索工具迁移测试\n")
    
    results = []
    
    # 测试1: 配置文件更新
    result1 = test_configuration_files()
    results.append(("配置文件更新", result1))
    
    # 测试2: 协调者Bot代码更新
    result2 = test_coordinator_bot_code()
    results.append(("协调者Bot代码更新", result2))
    
    # 测试3: 商品搜索工具
    result3 = test_product_search_tool()
    results.append(("商品搜索工具", result3))
    
    # 汇总结果
    print("\n📊 测试结果汇总:")
    print("=" * 60)
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 商品搜索工具迁移成功！")
        print("\n✨ 迁移要点:")
        print("1. ✅ 商品搜索工具已从子Agent配置中移除")
        print("2. ✅ 商品搜索工具已添加到主Agent（CoordinatorBot）")
        print("3. ✅ 配置文件已更新，包含引导说明")
        print("4. ✅ 协调者指令已更新，强调用户确认流程")
        print("5. ✅ 支持与用户交互确认具体SKU规格")
        
        print("\n🎯 使用场景:")
        print("- 用户询问商品信息时，主Agent可以直接搜索")
        print("- 搜索到多个SKU时，主Agent会与用户确认")
        print("- 确认后可以将SKU信息传递给专业Agent分析")
        print("- 子Agent遇到商品搜索需求时会引导用户")
        
        print("\n📋 迁移总结:")
        print("商品搜索工具已成功从子Agent迁移到主Agent，")
        print("现在只有主Agent能够进行商品搜索和用户交互确认，")
        print("这样可以确保SKU确认的准确性和用户体验的一致性。")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    print(f"\n退出代码: {0 if success else 1}")
    sys.exit(0 if success else 1)
