#!/usr/bin/env python3
"""
测试商品搜索工具迁移

验证商品搜索工具从子Agent迁移到主Agent（CoordinatorBot）的功能。
"""
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.agent.bots.coordinator_bot import CoordinatorBot
from src.services.agent.bots.data_fetcher_bot import DataFetcherBot
from src.utils.logger import logger


async def test_coordinator_has_product_search():
    """测试协调者Bot是否包含商品搜索工具"""
    print("🔍 测试协调者Bot的商品搜索工具")
    
    test_user_info = {
        "name": "测试用户",
        "job_title": "数据分析师",
        "user_id": "test_user_001",
        "department": "技术部",
        "permissions": ["sales_data", "warehouse_data"]
    }
    
    try:
        # 创建协调者Bot
        coordinator = CoordinatorBot(test_user_info)
        
        # 检查工具列表
        tool_names = []
        for tool in coordinator.available_tools:
            if hasattr(tool, '__name__'):
                tool_names.append(tool.__name__)
            elif hasattr(tool, 'name'):
                tool_names.append(tool.name)
            else:
                tool_names.append(str(type(tool).__name__))
        
        print(f"  协调者Bot工具列表: {tool_names}")
        
        # 检查是否包含商品搜索工具
        has_product_search = any(
            'search_product_by_name' in name or 'product' in name.lower()
            for name in tool_names
        )
        
        if has_product_search:
            print("  ✅ 协调者Bot包含商品搜索工具")
            return True
        else:
            print("  ❌ 协调者Bot未包含商品搜索工具")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试协调者Bot失败: {e}")
        logger.exception("测试协调者Bot商品搜索工具失败")
        return False


async def test_sub_agents_no_product_search():
    """测试子Agent是否已移除商品搜索工具"""
    print("\n🚫 测试子Agent是否已移除商品搜索工具")
    
    test_user_info = {
        "name": "测试用户",
        "job_title": "数据分析师",
        "user_id": "test_user_001",
        "department": "技术部",
        "permissions": ["sales_data", "warehouse_data"]
    }
    
    # 测试的配置文件
    test_configs = [
        "general_chat_bot.yml",
        "warehouse_and_fulfillment.yml"
    ]
    
    all_passed = True
    
    for config_file in test_configs:
        try:
            print(f"  测试 {config_file}:")
            
            # 创建DataFetcherBot
            bot = DataFetcherBot(test_user_info, config_file)
            
            # 检查配置中的工具
            tools_config = bot.config.get('tools', [])
            tool_names = [tool.get('name') for tool in tools_config if isinstance(tool, dict)]
            
            print(f"    配置的工具: {tool_names}")
            
            # 检查是否包含商品搜索工具
            has_product_search = 'search_product_by_name' in tool_names
            
            if not has_product_search:
                print(f"    ✅ {config_file} 已移除商品搜索工具")
            else:
                print(f"    ❌ {config_file} 仍包含商品搜索工具")
                all_passed = False
                
        except Exception as e:
            print(f"    ❌ 测试 {config_file} 失败: {e}")
            all_passed = False
    
    return all_passed


def test_product_search_tool_functionality():
    """测试商品搜索工具的基本功能"""
    print("\n🛠️ 测试商品搜索工具基本功能")
    
    try:
        from src.services.agent.tools.product_search_tool import search_product_by_name
        
        print("  ✅ 商品搜索工具导入成功")
        
        # 检查工具函数签名
        import inspect
        sig = inspect.signature(search_product_by_name)
        params = list(sig.parameters.keys())
        print(f"  工具参数: {params}")
        
        # 检查工具文档
        doc = search_product_by_name.__doc__
        if doc and "确认" in doc:
            print("  ✅ 工具文档包含用户确认相关说明")
        else:
            print("  ⚠️ 工具文档可能需要更新确认说明")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试商品搜索工具功能失败: {e}")
        return False


def test_configuration_updates():
    """测试配置文件更新"""
    print("\n📝 测试配置文件更新")
    
    try:
        # 检查general_chat_bot.yml
        with open("resources/data_fetcher_bot_config/general_chat_bot.yml", 'r', encoding='utf-8') as f:
            general_content = f.read()
        
        # 检查tools配置中是否包含search_product_by_name
        import yaml
        general_config = yaml.safe_load(general_content)
        tools_config = general_config.get('tools', [])
        tool_names = [tool.get('name') for tool in tools_config if isinstance(tool, dict)]

        if "search_product_by_name" not in tool_names:
            print("  ✅ general_chat_bot.yml 已移除商品搜索工具")
        else:
            print("  ❌ general_chat_bot.yml 仍包含商品搜索工具")
            return False
        
        if "主助手进行商品搜索" in general_content:
            print("  ✅ general_chat_bot.yml 已添加引导说明")
        else:
            print("  ⚠️ general_chat_bot.yml 可能缺少引导说明")
        
        # 检查warehouse_and_fulfillment.yml
        with open("resources/data_fetcher_bot_config/warehouse_and_fulfillment.yml", 'r', encoding='utf-8') as f:
            warehouse_content = f.read()

        warehouse_config = yaml.safe_load(warehouse_content)
        warehouse_tools = warehouse_config.get('tools', [])
        warehouse_tool_names = [tool.get('name') for tool in warehouse_tools if isinstance(tool, dict)]

        if "search_product_by_name" not in warehouse_tool_names:
            print("  ✅ warehouse_and_fulfillment.yml 已移除商品搜索工具")
        else:
            print("  ❌ warehouse_and_fulfillment.yml 仍包含商品搜索工具")
            return False
        
        if "主助手进行商品搜索" in warehouse_content:
            print("  ✅ warehouse_and_fulfillment.yml 已添加引导说明")
        else:
            print("  ⚠️ warehouse_and_fulfillment.yml 可能缺少引导说明")
        
        # 检查coordinator_instruction.md
        with open("resources/prompt/coordinator_instruction.md", 'r', encoding='utf-8') as f:
            coordinator_content = f.read()
        
        if "商品搜索 (search_product_by_name)" in coordinator_content:
            print("  ✅ coordinator_instruction.md 已添加商品搜索说明")
        else:
            print("  ❌ coordinator_instruction.md 缺少商品搜索说明")
            return False
        
        if "必须与用户确认具体SKU" in coordinator_content:
            print("  ✅ coordinator_instruction.md 包含用户确认说明")
        else:
            print("  ❌ coordinator_instruction.md 缺少用户确认说明")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试配置文件更新失败: {e}")
        return False


async def main():
    """主函数"""
    print("🚀 开始测试商品搜索工具迁移\n")
    
    results = []
    
    # 测试1: 协调者Bot包含商品搜索工具
    result1 = await test_coordinator_has_product_search()
    results.append(("协调者Bot商品搜索工具", result1))
    
    # 测试2: 子Agent移除商品搜索工具
    result2 = await test_sub_agents_no_product_search()
    results.append(("子Agent移除商品搜索工具", result2))
    
    # 测试3: 商品搜索工具基本功能
    result3 = test_product_search_tool_functionality()
    results.append(("商品搜索工具功能", result3))
    
    # 测试4: 配置文件更新
    result4 = test_configuration_updates()
    results.append(("配置文件更新", result4))
    
    # 汇总结果
    print("\n📊 测试结果汇总:")
    print("=" * 60)
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 商品搜索工具迁移成功！")
        print("\n✨ 迁移要点:")
        print("1. ✅ 商品搜索工具已从子Agent移除")
        print("2. ✅ 商品搜索工具已添加到主Agent（CoordinatorBot）")
        print("3. ✅ 配置文件已更新，包含引导说明")
        print("4. ✅ 协调者指令已更新，强调用户确认流程")
        print("5. ✅ 支持与用户交互确认具体SKU规格")
        
        print("\n🎯 使用场景:")
        print("- 用户询问商品信息时，主Agent可以直接搜索")
        print("- 搜索到多个SKU时，主Agent会与用户确认")
        print("- 确认后可以将SKU信息传递给专业Agent分析")
        print("- 子Agent遇到商品搜索需求时会引导用户")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    return all_passed


if __name__ == "__main__":
    success = asyncio.run(main())
    print(f"\n退出代码: {0 if success else 1}")
    sys.exit(0 if success else 1)
