#!/usr/bin/env python3
"""
最终的商品搜索工具分工测试

验证商品搜索工具的最终分工策略：
- CoordinatorBot: 有商品搜索，专注SKU确认
- general_chat_bot: 有商品搜索，专注商品推荐
- warehouse_and_fulfillment: 无商品搜索，专注库存物流
"""
import yaml
import sys
from pathlib import Path


def test_agent_tool_distribution():
    """测试Agent工具分配"""
    print("🔧 测试Agent工具分配")
    
    try:
        # 检查general_chat_bot.yml - 应该有商品搜索
        with open("resources/data_fetcher_bot_config/general_chat_bot.yml", 'r', encoding='utf-8') as f:
            general_config = yaml.safe_load(f)
        
        general_tools = [tool.get('name') for tool in general_config.get('tools', [])]
        
        if "search_product_by_name" in general_tools:
            print("  ✅ general_chat_bot 保留商品搜索工具（用于商品推荐）")
        else:
            print("  ❌ general_chat_bot 缺少商品搜索工具")
            return False
        
        # 检查warehouse_and_fulfillment.yml - 应该没有商品搜索
        with open("resources/data_fetcher_bot_config/warehouse_and_fulfillment.yml", 'r', encoding='utf-8') as f:
            warehouse_config = yaml.safe_load(f)
        
        warehouse_tools = [tool.get('name') for tool in warehouse_config.get('tools', [])]
        
        if "search_product_by_name" not in warehouse_tools:
            print("  ✅ warehouse_and_fulfillment 已移除商品搜索工具（专注库存物流）")
        else:
            print("  ❌ warehouse_and_fulfillment 仍包含商品搜索工具")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试Agent工具分配失败: {e}")
        return False


def test_agent_descriptions():
    """测试Agent描述更新"""
    print("\n📝 测试Agent描述更新")
    
    try:
        # 检查general_chat_bot描述
        with open("resources/data_fetcher_bot_config/general_chat_bot.yml", 'r', encoding='utf-8') as f:
            general_content = f.read()
        
        if "search_product_by_name" in general_content and "推荐" in general_content:
            print("  ✅ general_chat_bot 描述包含商品搜索和推荐功能")
        else:
            print("  ❌ general_chat_bot 描述缺少商品搜索或推荐说明")
            return False
        
        if "主助手进行详细确认" in general_content:
            print("  ✅ general_chat_bot 包含引导到主助手的说明")
        else:
            print("  ⚠️ general_chat_bot 可能缺少引导说明")
        
        # 检查warehouse_and_fulfillment描述
        with open("resources/data_fetcher_bot_config/warehouse_and_fulfillment.yml", 'r', encoding='utf-8') as f:
            warehouse_content = f.read()
        
        if "主助手进行商品搜索" in warehouse_content:
            print("  ✅ warehouse_and_fulfillment 包含引导到主助手的说明")
        else:
            print("  ❌ warehouse_and_fulfillment 缺少引导说明")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试Agent描述更新失败: {e}")
        return False


def test_coordinator_instructions():
    """测试协调者指令更新"""
    print("\n🎯 测试协调者指令更新")
    
    try:
        with open("resources/prompt/coordinator_instruction.md", 'r', encoding='utf-8') as f:
            coordinator_content = f.read()
        
        if "知识库检索和商品推荐 (general_chat_bot)" in coordinator_content:
            print("  ✅ 协调者指令明确了general_chat_bot的商品推荐功能")
        else:
            print("  ❌ 协调者指令缺少general_chat_bot商品推荐说明")
            return False
        
        if "商品搜索和SKU确认" in coordinator_content:
            print("  ✅ 协调者指令强调了SKU确认功能")
        else:
            print("  ❌ 协调者指令缺少SKU确认说明")
            return False
        
        if "主Agent的优势" in coordinator_content:
            print("  ✅ 协调者指令说明了主Agent在SKU确认方面的优势")
        else:
            print("  ❌ 协调者指令缺少主Agent优势说明")
            return False
        
        if "推荐一些烘焙用的奶油" in coordinator_content:
            print("  ✅ 协调者指令包含商品推荐示例")
        else:
            print("  ❌ 协调者指令缺少商品推荐示例")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试协调者指令更新失败: {e}")
        return False


def test_coordinator_bot_code():
    """测试协调者Bot代码"""
    print("\n🤖 测试协调者Bot代码")
    
    try:
        with open("src/services/agent/bots/coordinator_bot.py", 'r', encoding='utf-8') as f:
            coordinator_code = f.read()
        
        if "_add_product_search_tools" in coordinator_code:
            print("  ✅ 协调者Bot包含商品搜索工具添加方法")
        else:
            print("  ❌ 协调者Bot缺少商品搜索工具添加方法")
            return False
        
        if "search_product_by_name" in coordinator_code:
            print("  ✅ 协调者Bot导入了商品搜索工具")
        else:
            print("  ❌ 协调者Bot未导入商品搜索工具")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试协调者Bot代码失败: {e}")
        return False


def summarize_final_architecture():
    """总结最终架构"""
    print("\n📋 最终架构总结")
    print("=" * 60)
    
    print("🎯 商品搜索工具分工：")
    print()
    print("1. **CoordinatorBot (主协调助手)**")
    print("   - ✅ 拥有商品搜索工具")
    print("   - 🎯 专长：与用户交互确认具体SKU规格")
    print("   - 📝 适用：用户需要精确选择商品规格时")
    print("   - 💡 优势：可以进行多轮对话确认")
    print()
    
    print("2. **general_chat_bot (知识问答助手)**")
    print("   - ✅ 拥有商品搜索工具")
    print("   - 🎯 专长：商品推荐和产品知识解答")
    print("   - 📝 适用：用户询问商品推荐、产品知识时")
    print("   - 💡 优势：结合产品知识进行智能推荐")
    print()
    
    print("3. **warehouse_and_fulfillment (仓储物流助手)**")
    print("   - ❌ 不拥有商品搜索工具")
    print("   - 🎯 专长：库存查询、物流跟踪、订单履行")
    print("   - 📝 适用：库存、配送、入库等物流相关问题")
    print("   - 💡 优势：专注仓储物流，避免功能混乱")
    print()
    
    print("🔄 协作流程：")
    print("- 用户询问商品推荐 → general_chat_bot 直接处理")
    print("- 用户需要确认SKU → CoordinatorBot 交互确认")
    print("- 用户查询库存 → warehouse_and_fulfillment 处理")
    print("- 需要商品搜索的库存查询 → 引导用户先确认SKU")
    print()
    
    print("✨ 这种分工的优势：")
    print("1. 职责清晰：每个Agent专注自己的核心功能")
    print("2. 用户体验好：推荐和确认分别优化")
    print("3. 避免冲突：库存Agent不处理商品搜索")
    print("4. 灵活协作：可以引导用户到合适的Agent")


def main():
    """主函数"""
    print("🚀 开始最终的商品搜索工具分工测试\n")
    
    results = []
    
    # 测试1: Agent工具分配
    result1 = test_agent_tool_distribution()
    results.append(("Agent工具分配", result1))
    
    # 测试2: Agent描述更新
    result2 = test_agent_descriptions()
    results.append(("Agent描述更新", result2))
    
    # 测试3: 协调者指令更新
    result3 = test_coordinator_instructions()
    results.append(("协调者指令更新", result3))
    
    # 测试4: 协调者Bot代码
    result4 = test_coordinator_bot_code()
    results.append(("协调者Bot代码", result4))
    
    # 汇总结果
    print("\n📊 测试结果汇总:")
    print("=" * 60)
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("🎉 商品搜索工具分工配置成功！")
        summarize_final_architecture()
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    print(f"\n退出代码: {0 if success else 1}")
    sys.exit(0 if success else 1)
