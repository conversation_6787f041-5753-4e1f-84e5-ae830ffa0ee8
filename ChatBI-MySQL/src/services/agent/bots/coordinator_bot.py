"""
协调者Bot实现 - Agent as Tool架构

这个Bot作为主协调者，负责：
1. 接收用户查询并保持对话主体地位
2. 分析查询内容，决定调用哪些专业Agent工具
3. 协调多个专业Agent的执行（支持并行）
4. 聚合和整理专业Agent的结果
5. 统一错误处理和重试机制
"""
import json
from typing import Optional, Dict, Any, List

from agents import Agent, Model, ModelSettings
from src.services.agent.bots.base_bot import BaseBot
from src.services.agent.utils.agent_tools import AgentToolRegistry, create_parallel_analysis_tool
from src.services.agent.utils.model_provider import LITE_LLM_MODEL
from src.services.agent.tools.tool_manager import tool_manager
from src.utils.logger import logger
from src.utils.resource_manager import load_resource


class CoordinatorBot(BaseBot):
    """
    协调者Bot - Agent as Tool架构的核心
    
    负责协调多个专业Agent工具，实现统一的用户交互界面。
    相比原来的MasterControllerBot，这个实现：
    - 移除了handoff机制
    - 使用工具调用方式协调专业Agent
    - 支持并行调用多个Agent
    - 集中式错误处理和上下文管理
    """

    def __init__(self, user_info: Dict[str, Any]):
        super().__init__(user_info)
        # 初始化Agent工具注册中心
        self.tool_registry = AgentToolRegistry(user_info)
        self.available_tools = []
        self._initialize_tools()

    def _initialize_tools(self):
        """初始化所有可用的Agent工具"""
        try:
            # 注册数据获取Agent工具
            data_fetcher_tools = self.tool_registry.register_data_fetcher_tools()
            self.available_tools.extend(data_fetcher_tools)

            # 添加并行分析工具
            parallel_tool = create_parallel_analysis_tool(self.tool_registry)
            self.available_tools.append(parallel_tool)

            # 添加商品搜索工具（需要用户交互确认SKU）
            self._add_product_search_tools()

            logger.info(f"🔧 协调者Bot初始化完成，共注册{len(self.available_tools)}个工具")

        except Exception as e:
            logger.exception(f"初始化Agent工具失败: {e}")
            self.available_tools = []

    def _add_product_search_tools(self):
        """添加商品搜索相关工具到主Agent"""
        try:
            # 从tool_manager获取商品搜索工具
            from src.services.agent.tools.product_search_tool import search_product_by_name

            # 直接添加商品搜索工具
            self.available_tools.append(search_product_by_name)

            logger.info("📦 已添加商品搜索工具到协调者Bot")

        except Exception as e:
            logger.exception(f"添加商品搜索工具失败: {e}")

    def get_description(self) -> str:
        return "我是智能协调助手，能够分析您的问题并调用最合适的专业分析工具来为您提供准确的答案。我可以处理销售分析、仓储物流、知识查询等多个领域的问题。"

    def get_available_tools_description(self) -> str:
        """获取可用工具的描述信息"""
        if not self.tool_registry.registered_tools:
            return "暂无可用的专业分析工具"
        
        descriptions = []
        for tool_name, tool_func in self.tool_registry.registered_tools.items():
            # 尝试获取工具的描述信息
            desc = getattr(tool_func, 'description', f"{tool_name}专业分析工具")
            descriptions.append(f"- {tool_name}: {desc}")
        
        return "\n".join(descriptions)

    def create_agent(self, model: Optional[Model] = LITE_LLM_MODEL) -> Agent:
        """
        创建协调者Agent实例
        
        Args:
            model: 使用的模型实例
            
        Returns:
            Agent: 配置好的协调者Agent
        """
        # 加载基础指令
        base_instruction = load_resource("prompt", "coordinator_instruction.md")
        
        # 构建完整指令，包含用户上下文和工具信息
        tools_description = self.get_available_tools_description()
        realtime_instruction = self.get_user_realtime_instruction()
        
        full_instruction = f"""
{base_instruction}

## 当前可用的专业分析工具
{tools_description}

## 用户上下文信息
{realtime_instruction}
        """.strip()
        
        logger.info(f"协调者Bot使用模型: {model}")
        logger.info(f"协调者Bot工具数量: {len(self.available_tools)}")
        
        # 创建Agent实例
        agent = Agent(
            name="智能协调助手",
            instructions=full_instruction,
            model=model,
            tools=self.available_tools,
            model_settings=ModelSettings(
                parallel_tool_calls=True,  # 启用并行工具调用
                tool_choice="auto",        # 自动选择工具
                temperature=0.1            # 低温度确保稳定性
            )
        )
        
        return agent

    def get_tool_usage_stats(self) -> Dict[str, Any]:
        """获取工具使用统计信息"""
        return {
            "total_tools": len(self.available_tools),
            "registered_agents": list(self.tool_registry.registered_tools.keys()),
            "has_parallel_tool": any(
                getattr(tool, '__name__', '').startswith('parallel_analysis') 
                for tool in self.available_tools
            )
        }

    def validate_tools(self) -> List[str]:
        """验证工具配置，返回问题列表"""
        issues = []
        
        if not self.available_tools:
            issues.append("没有可用的专业分析工具")
        
        if not self.tool_registry.registered_tools:
            issues.append("没有注册任何Agent工具")
        
        # 检查工具函数的有效性
        for tool_name, tool_func in self.tool_registry.registered_tools.items():
            # FunctionTool对象也是有效的工具
            if not (callable(tool_func) or hasattr(tool_func, '__call__')):
                issues.append(f"工具 {tool_name} 不是可调用对象")
        
        return issues

    def get_user_realtime_instruction(self) -> str:
        """
        重写父类方法，为协调者Agent定制实时指令
        """
        base_instruction = super().get_user_realtime_instruction()

        # 添加协调者特有的指令
        coordinator_instruction = f"""
{base_instruction}

## 协调者特殊指令
- 你是智能协调助手，负责分析用户问题并调用合适的专业工具
- 当问题涉及多个领域时，可以使用并行分析工具同时调用多个专业Agent
- 始终要调用工具来获取数据，不要凭空回答问题
- 如果工具执行失败，要向用户说明情况并建议替代方案
- 要对工具返回的结果进行整理和总结，提供清晰易懂的答案
- 专业Agent工具只需要传递用户的具体查询问题，不需要传递完整对话历史
        """

        return coordinator_instruction.strip()

    def get_execution_summary(self) -> Dict[str, Any]:
        """获取执行摘要信息"""
        return {
            "coordinator_info": {
                "user_name": self.user_name,
                "job_title": self.job_title,
                "available_tools": len(self.available_tools),
                "registered_agents": list(self.tool_registry.registered_tools.keys())
            },
            "executor_stats": self.tool_registry.executor.get_execution_stats(),
            "tool_validation": self.validate_tools()
        }
