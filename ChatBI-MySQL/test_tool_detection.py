#!/usr/bin/env python3
"""
测试新的工具调用检测逻辑

验证Agent as Tool架构下的工具调用检测和日志记录功能。
"""
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.agent.runner import _extract_agent_name_from_tool_output


def test_agent_name_extraction():
    """测试Agent名称提取功能"""
    print("🧪 测试Agent名称提取功能")
    
    test_cases = [
        # JSON格式的工具输出
        {
            "input": '{"success": true, "agent_name": "sales_order_analytics", "data": "销售数据"}',
            "expected": "sales_order_analytics"
        },
        # 包含已知Agent名称的文本
        {
            "input": "warehouse_and_fulfillment analysis completed successfully",
            "expected": "warehouse_and_fulfillment"
        },
        # 包含analysis关键词
        {
            "input": "Analysis completed with detailed results",
            "expected": "专业分析工具"
        },
        # 通用情况
        {
            "input": "Tool execution completed",
            "expected": "专业工具"
        },
        # 错误的JSON
        {
            "input": '{"invalid": json}',
            "expected": "专业工具"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        result = _extract_agent_name_from_tool_output(case["input"])
        status = "✅" if result == case["expected"] else "❌"
        print(f"  测试 {i}: {status} 输入: {case['input'][:50]}...")
        print(f"         期望: {case['expected']}, 实际: {result}")
        if result != case["expected"]:
            print(f"         ❌ 测试失败!")
        print()


def test_tool_output_formats():
    """测试不同格式的工具输出"""
    print("🔧 测试工具输出格式")
    
    # 模拟AgentToolExecutor返回的JSON格式
    sample_outputs = [
        {
            "name": "销售分析工具输出",
            "output": """{
  "success": true,
  "agent_name": "sales_order_analytics",
  "data": "查询到100条销售记录",
  "sql": "SELECT * FROM orders WHERE ...",
  "error": null,
  "execution_time": 2.5
}"""
        },
        {
            "name": "仓储物流工具输出", 
            "output": """{
  "success": true,
  "agent_name": "warehouse_and_fulfillment",
  "data": "库存查询完成",
  "sql": null,
  "error": null,
  "execution_time": 1.8
}"""
        },
        {
            "name": "知识问答工具输出",
            "output": """{
  "success": true,
  "agent_name": "general_chat_bot",
  "data": "找到相关文档3篇",
  "sql": null,
  "error": null,
  "execution_time": 0.9
}"""
        },
        {
            "name": "工具执行失败",
            "output": """{
  "success": false,
  "agent_name": "sales_order_analytics",
  "data": null,
  "sql": null,
  "error": "数据库连接超时",
  "execution_time": 30.0
}"""
        }
    ]
    
    for sample in sample_outputs:
        print(f"  📋 {sample['name']}:")
        agent_name = _extract_agent_name_from_tool_output(sample["output"])
        print(f"     提取的Agent名称: {agent_name}")
        
        # 模拟检测逻辑
        is_tool_call = "agent_name" in sample["output"]
        print(f"     工具调用检测: {'✅ 检测到' if is_tool_call else '❌ 未检测到'}")
        print()


def simulate_runner_detection():
    """模拟runner中的检测逻辑"""
    print("🎯 模拟runner检测逻辑")
    
    # 模拟不同的事件流
    event_scenarios = [
        {
            "name": "成功的工具调用场景",
            "events": [
                {"type": "log", "content": "开始处理用户查询"},
                {"type": "tool_output", "content": '{"success": true, "agent_name": "sales_order_analytics", "data": "查询完成"}'},
                {"type": "log", "content": "查询处理完成"}
            ]
        },
        {
            "name": "多个工具调用场景",
            "events": [
                {"type": "log", "content": "开始并行分析"},
                {"type": "tool_output", "content": '{"success": true, "agent_name": "sales_order_analytics", "data": "销售分析完成"}'},
                {"type": "tool_output", "content": '{"success": true, "agent_name": "warehouse_and_fulfillment", "data": "库存分析完成"}'},
                {"type": "log", "content": "所有分析完成"}
            ]
        },
        {
            "name": "无工具调用场景",
            "events": [
                {"type": "log", "content": "开始处理"},
                {"type": "log", "content": "处理中..."},
                {"type": "log", "content": "处理完成"}
            ]
        },
        {
            "name": "工具调用失败场景",
            "events": [
                {"type": "log", "content": "开始查询"},
                {"type": "tool_output", "content": '{"success": false, "agent_name": "sales_order_analytics", "error": "查询失败"}'},
                {"type": "log", "content": "查询结束"}
            ]
        }
    ]
    
    for scenario in event_scenarios:
        print(f"  📊 {scenario['name']}:")
        
        # 模拟检测逻辑
        tool_calls_detected = False
        agent_execution_logs = []
        collected_logs = ""
        
        for event in scenario["events"]:
            msg_type = event.get("type")
            content = event.get("content", "")
            
            # 收集日志
            if msg_type in ["log", "handoff_log", "tool_output"] and content:
                collected_logs += str(content) + "\n"
            
            # 检测工具调用
            if msg_type == "tool_output" and content:
                tool_calls_detected = True
                agent_name = _extract_agent_name_from_tool_output(content)
                if agent_name:
                    agent_log = f"🔄 CoordinatorBot调用专业工具: {agent_name}"
                    agent_execution_logs.append(agent_log)
        
        # 判断执行结果
        if tool_calls_detected or len(collected_logs.strip()) > 0:
            status = "✅ 执行成功"
            if agent_execution_logs:
                status += f"，调用了 {len(agent_execution_logs)} 个专业工具"
        else:
            status = "❌ 未检测到有效输出，需要重试"
        
        print(f"     检测结果: {status}")
        for log in agent_execution_logs:
            print(f"     日志: {log}")
        print()


def main():
    """主函数"""
    print("🚀 开始测试工具调用检测逻辑\n")
    
    test_agent_name_extraction()
    test_tool_output_formats()
    simulate_runner_detection()
    
    print("✅ 所有测试完成")
    print("\n📋 总结:")
    print("1. ✅ Agent名称提取功能正常")
    print("2. ✅ 工具输出格式解析正常") 
    print("3. ✅ runner检测逻辑模拟正常")
    print("4. ✅ 日志记录格式保持一致")


if __name__ == "__main__":
    main()
