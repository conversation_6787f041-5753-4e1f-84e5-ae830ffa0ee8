#!/usr/bin/env python3
"""
端到端测试Agent as Tool架构

测试从用户查询到工具调用检测的完整流程。
"""
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.agent.bots.coordinator_bot import CoordinatorBot
from src.services.feishu.user_service import UserService
from src.utils.logger import logger


async def test_coordinator_bot_execution():
    """测试协调者Bot的执行"""
    print("🤖 测试协调者Bot执行")
    
    # 模拟用户信息
    test_user_info = {
        "name": "测试用户",
        "job_title": "数据分析师",
        "user_id": "test_user_001",
        "department": "技术部",
        "permissions": ["sales_data", "warehouse_data"]
    }
    
    try:
        # 创建协调者Bot
        bot = CoordinatorBot(test_user_info)
        print(f"✅ 协调者Bot创建成功，注册了{len(bot.available_tools)}个工具")
        
        # 创建Agent实例
        agent = bot.create_agent()
        print(f"✅ Agent实例创建成功: {agent.name}")
        
        # 验证工具配置
        issues = bot.validate_tools()
        if issues:
            print(f"⚠️ 工具配置问题: {issues}")
        else:
            print("✅ 工具配置验证通过")
        
        # 获取执行摘要
        summary = bot.get_execution_summary()
        print(f"📊 执行摘要:")
        print(f"   - 用户: {summary['coordinator_info']['user_name']}")
        print(f"   - 可用工具: {summary['coordinator_info']['available_tools']}")
        print(f"   - 注册Agent: {summary['coordinator_info']['registered_agents']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 协调者Bot测试失败: {e}")
        return False


def test_detection_logic():
    """测试检测逻辑"""
    print("\n🔍 测试检测逻辑")
    
    from src.services.agent.runner import _extract_agent_name_from_tool_output
    from src.services.feishu.stream_processor import StreamProcessor
    
    # 测试Agent名称提取
    test_outputs = [
        '{"success": true, "agent_name": "sales_order_analytics", "data": "查询完成"}',
        '{"success": true, "agent_name": "warehouse_and_fulfillment", "data": "库存查询完成"}',
        'warehouse_and_fulfillment analysis completed',
        'Analysis completed successfully'
    ]
    
    print("  Agent名称提取测试:")
    for output in test_outputs:
        agent_name = _extract_agent_name_from_tool_output(output)
        print(f"    输入: {output[:50]}...")
        print(f"    提取: {agent_name}")
    
    # 测试流处理器重试逻辑
    processor = StreamProcessor()
    test_logs = [
        "🔄 CoordinatorBot调用专业工具: sales_order_analytics",
        "专业工具 warehouse_and_fulfillment 执行完成",
        "Analysis completed with detailed results for user query",
        "简短输出",
        ""
    ]
    
    print("\n  流处理器重试逻辑测试:")
    for log in test_logs:
        should_retry = processor._should_retry(log, 0)
        status = "需要重试" if should_retry else "执行成功"
        print(f"    日志: {log[:50]}...")
        print(f"    结果: {status}")
    
    return True


def test_tool_output_format():
    """测试工具输出格式"""
    print("\n📋 测试工具输出格式")
    
    # 模拟AgentToolExecutor的输出格式
    sample_tool_outputs = [
        {
            "success": True,
            "agent_name": "sales_order_analytics",
            "data": "查询到100条销售记录",
            "sql": "SELECT * FROM orders WHERE order_time >= '2025-01-01'",
            "error": None,
            "execution_time": 2.5
        },
        {
            "success": False,
            "agent_name": "warehouse_and_fulfillment", 
            "data": None,
            "sql": None,
            "error": "数据库连接超时",
            "execution_time": 30.0
        }
    ]
    
    import json
    for i, output in enumerate(sample_tool_outputs, 1):
        json_output = json.dumps(output, ensure_ascii=False, indent=2)
        print(f"  工具输出 {i}:")
        print(f"    JSON格式: {json_output[:100]}...")
        
        # 测试检测
        from src.services.agent.runner import _extract_agent_name_from_tool_output
        agent_name = _extract_agent_name_from_tool_output(json_output)
        print(f"    提取Agent: {agent_name}")
        print(f"    执行状态: {'成功' if output['success'] else '失败'}")
    
    return True


async def main():
    """主函数"""
    print("🚀 开始端到端测试\n")
    
    results = []
    
    # 测试1: 协调者Bot执行
    result1 = await test_coordinator_bot_execution()
    results.append(("协调者Bot执行", result1))
    
    # 测试2: 检测逻辑
    result2 = test_detection_logic()
    results.append(("检测逻辑", result2))
    
    # 测试3: 工具输出格式
    result3 = test_tool_output_format()
    results.append(("工具输出格式", result3))
    
    # 汇总结果
    print("\n📊 测试结果汇总:")
    print("=" * 50)
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 所有测试通过！Agent as Tool架构工作正常")
        print("\n主要改进:")
        print("1. ✅ 移除了handoff检测，改为工具调用检测")
        print("2. ✅ 保持了原有的日志格式和UI体验")
        print("3. ✅ 支持多种检测方式，提高成功率")
        print("4. ✅ 错误处理和重试机制正常工作")
    else:
        print("❌ 部分测试失败，需要进一步调试")
    
    return all_passed


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
